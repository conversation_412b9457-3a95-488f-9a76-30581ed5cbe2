# 项目对外发送MQ消息对应关系表

## 概述
本文档整理了jdl-oms-express项目中所有对外发送的MQ消息及其对应的消息类和转换类。

## 对应关系表

### 1. 订单生命周期消息

| MQ Topic | 消息类 | 转换类 | 说明 |
|----------|--------|--------|------|
| `EXPRESS_ORDER_CREATE_NOTICE` | `CreateOrderNotice` | `CreateOrderNoticeTranslator` | 订单接单成功通知 |
| `EXPRESS_ORDER_STATUS` | `OrderStatusNotifyMessageDto` | `OrderStatusNotifyDataDtoTranslator` | 订单状态变更通知 |
| `EXPRESS_ORDER_DATA` | `OrderDataFlowDto` | `OrderDataNotifyTranslator` | 订单数据流水记录 |
| `EXPRESS_ORDER_DATA_UPDATE_NOTICE` | `OrderDataUpdateDto` | `OrderDataNotifyTranslator` | 订单数据变更通知 |


## 消息类命名规范

根据分析，项目中的消息类遵循以下命名规范：

1. **EXPRESS_ORDER_CREATE_NOTICE** → `CreateOrderNotice`
2. **其他消息** → 通常以 `MessageDto` 或 `JmqMessageDto` 结尾

## 转换类命名规范

转换类通常遵循以下规范：
1. 消息类名 + `Translator`
2. 例如：`CreateOrderNoticeTranslator`、`CommonAsyncEnquiryJmqTranslator`

## 注意事项

1. 本表只包含本系统对外发送的MQ消息，不包含监听外部系统的MQ消息
2. 转换类分为两种类型：
   - 使用 `@Translator` 注解的转换类：如 `CreateOrderNoticeTranslator`、`OrderDataNotifyTranslator` 等
   - Facade类中的转换方法：如 `WaitSalesConfirmJmqFacade`、`EnquiryNoticeCRMJmqFacade` 等
3. 部分消息类没有专门的转换类，直接在业务代码中构建
4. 消息类都继承自 `CommonDto` 基类
5. 所有MQ消息发送都通过 `JMQMessageProducer` 实现
